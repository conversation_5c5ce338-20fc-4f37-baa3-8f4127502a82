#ifndef OLED_DISPLAY_H
#define OLED_DISPLAY_H

#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// OLED display configuration
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 32
#define OLED_RESET -1  // Reset pin (not used)
#define SCREEN_ADDRESS 0x3C  // Common I2C address for 128x32 OLED

extern Adafruit_SSD1306 display;

void initDisplay();
void displayThermalData(float* pixels, float maxTemp, float minTemp);
void displayMessage(const char* message);
void clearDisplay();

#endif
