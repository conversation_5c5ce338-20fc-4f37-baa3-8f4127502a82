#include "oled_display.h"
#include "thermal_sensor.h"
#include <Wire.h>

// Create a separate Wire instance for OLED
TwoWire OLEDWire = TwoWire(1);
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &OLEDWire, OLED_RESET);

void initDisplay() {
  // Initialize I2C for OLED on separate pins
  OLEDWire.begin(6, 7);  // SDA=GPIO6, SCL=GPIO7

  // Initialize the OLED display
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
    Serial.println(F("SSD1306 allocation failed"));
    return;
  }
  
  // Clear the display buffer
  display.clearDisplay();
  
  // Set text properties
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // Show initial message
  display.setCursor(0, 0);
  display.println(F("Thermal Sensor"));
  display.println(F("OLED Ready!"));
  display.display();
  
  delay(2000);
  display.clearDisplay();
}

void displayThermalData(float* pixels, float maxTemp, float minTemp, const char* url) {
  display.clearDisplay();

  // Display URL at the top
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(url);

  // Display current max temperature prominently
  display.setCursor(0, 10);
  display.print(F("Max Now: "));
  display.print(maxTemp, 1);
  display.println(F("C"));

  // Display min temperature
  display.setCursor(0, 20);
  display.print(F("Min: "));
  display.print(minTemp, 1);
  display.print(F("C"));
  
  // Simple thermal visualization (8x8 grid represented as dots)
  int startX = 85;
  int startY = 10;
  int dotSpacing = 5;

  for (int i = 0; i < 8; i++) {
    for (int j = 0; j < 8; j++) {
      int pixelIndex = i * 8 + j;
      float temp = pixels[pixelIndex];

      // Map temperature to display intensity (0-2 for dot size)
      int intensity = map(temp * 10, minTemp * 10, maxTemp * 10, 0, 2);
      intensity = constrain(intensity, 0, 2);

      int x = startX + j * dotSpacing;
      int y = startY + i * 2;

      // Draw dots based on intensity
      if (intensity > 0) {
        display.drawPixel(x, y, SSD1306_WHITE);
        if (intensity > 1) {
          display.drawPixel(x+1, y, SSD1306_WHITE);
          display.drawPixel(x, y+1, SSD1306_WHITE);
          display.drawPixel(x+1, y+1, SSD1306_WHITE);
        }
      }
    }
  }
  
  display.display();
}

void displayMessage(const char* message) {
  display.clearDisplay();
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(message);
  display.display();
}

void clearDisplay() {
  display.clearDisplay();
  display.display();
}
