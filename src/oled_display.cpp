#include "oled_display.h"
#include "thermal_sensor.h"
#include <Wire.h>

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

void initDisplay() {
  // Initialize the OLED display
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
    Serial.println(F("SSD1306 allocation failed"));
    return;
  }
  
  // Clear the display buffer
  display.clearDisplay();
  
  // Set text properties
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // Show initial message
  display.setCursor(0, 0);
  display.println(F("Thermal Sensor"));
  display.println(F("OLED Ready!"));
  display.display();
  
  delay(2000);
  display.clearDisplay();
}

void displayThermalData(float* pixels, float maxTemp, float minTemp) {
  display.clearDisplay();
  
  // Display title
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(F("Thermal Sensor"));
  
  // Display temperature range
  display.setCursor(0, 10);
  display.print(F("Max: "));
  display.print(maxTemp, 1);
  display.println(F("C"));
  
  display.setCursor(0, 20);
  display.print(F("Min: "));
  display.print(minTemp, 1);
  display.println(F("C"));
  
  // Simple thermal visualization (8x8 grid represented as bars)
  int startX = 80;
  int startY = 8;
  int barWidth = 2;
  int barHeight = 3;
  
  for (int i = 0; i < 8; i++) {
    for (int j = 0; j < 8; j++) {
      int pixelIndex = i * 8 + j;
      float temp = pixels[pixelIndex];
      
      // Map temperature to display intensity (0-3 pixels high)
      int intensity = map(temp * 10, minTemp * 10, maxTemp * 10, 0, 3);
      intensity = constrain(intensity, 0, 3);
      
      int x = startX + j * barWidth;
      int y = startY + i * barHeight;
      
      // Draw bars based on intensity
      for (int k = 0; k < intensity; k++) {
        display.drawPixel(x, y + k, SSD1306_WHITE);
        display.drawPixel(x + 1, y + k, SSD1306_WHITE);
      }
    }
  }
  
  display.display();
}

void displayMessage(const char* message) {
  display.clearDisplay();
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(message);
  display.display();
}

void clearDisplay() {
  display.clearDisplay();
  display.display();
}
